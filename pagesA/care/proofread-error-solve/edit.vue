<template>
    <ut-page class="page">
        <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
            <f-navbar fontColor="#fff" :bgColor="colors" title="校对异常处理" navbarType="1"></f-navbar>
        </ut-top>

        <view class="padding-lr-xs">
            <!-- 客户信息 -->
            <view class="cu-card margin-lr-sm margin-tb-sm radius bg-white flex padding-sm">
                <view class="flex-sub-0 margin-right-sm">
                    <u-lazy-load v-if="form.imgHead"
                        :image="$tools.showImg(form.imgHead)"
                        width="120"
                        height="160"
                        border-radius="4" />
                </view>
                <view class="flex-sub text-content">
                    <view class="flex justify-between align-center">
                        <view>
                            <view class="flex justify-start align-center">
                                <view class="text-df text-bold text-black">{{ form.name }}</view>
                                <view class="text-blue margin-left-lg" v-if="form.sex == 1">男
                                    <text class="cuIcon-male" />
                                </view>
                                <view class="text-pink margin-left-lg" v-if="form.sex == 2">女
                                    <text class="cuIcon-female" />
                                </view>
                            </view>
                            <view class="text-gray margin-top-xs">
                                <text v-if="form.phone" class="cuIcon-phone margin-right-xs" />
                                {{ form.phone }}
                            </view>
                        </view>
                        <view class="text-right">
                            <view class="text-gray">护理日期</view>
                            <view class="text-df text-bold" :style="{ color: colors }">{{ form.workDate }}</view>
                        </view>
                    </view>

                    <view class="text-xs text-gray text-cut margin-top-xs">{{ form.address }}</view>
                    <view v-if="form.attendantName" class="margin-top-xs">
                        <text class="text-sm" :style="{ color: colors }">({{ form.groupName }})</text>
                        <text class="text-sm text-gray">护理员：{{ form.attendantName }}</text>
                    </view>

                    <view v-if="form.idcard" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">证件号码：</text>
                        <text class="text-sm text-black">{{ form.idcard }}</text>
                    </view>

                    <!-- 额外信息 -->
                    <view v-if="form.schedulingDuration" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">排班时长：</text>
                        <text class="text-sm text-black">{{ form.schedulingDuration }}</text>
                    </view>

                    <view v-if="form.totalDuration" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">实际时长：</text>
                        <text class="text-sm text-black">{{ form.totalDuration }}</text>
                    </view>

                    <view v-if="form.checkInTime" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">签到时间：</text>
                        <text class="text-sm text-black">{{ form.checkInTime }}</text>
                    </view>

                    <view v-if="form.checkOutTime" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">签退时间：</text>
                        <text class="text-sm text-black">{{ form.checkOutTime }}</text>
                    </view>

                    <view v-if="form.lastManualUserName" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">校对人：</text>
                        <text class="text-sm text-black">{{ form.lastManualUserName }}</text>
                    </view>

                    <view v-if="form.lastManualTime" class="flex align-center margin-top-xs">
                        <text class="text-sm text-gray margin-right-xs">校对时间：</text>
                        <text class="text-sm text-black">{{ form.lastManualTime }}</text>
                    </view>

                    <view v-if="form.proofreadErrorRemark" class="margin-top-xs">
                        <text class="text-sm text-gray">校对异常信息：</text>
                        <text class="cu-tag sm bg-orange light radius">
                            {{ form.proofreadErrorRemark }}
                        </text>
                    </view>
                </view>
            </view>

            <!-- 服务项目 -->
            <view class="cu-card margin-lr-sm margin-tb-sm radius bg-white padding-sm">
                <view class="flex justify-between align-center margin-bottom-sm">
                    <view class="text-df text-bold text-lg">服务项目</view>
                    <view>
                        <u-button type="primary"
                            :color="colors"
                            text="增加服务项目"
                            :plain="true"
                            size="mini"
                            @click="addProject" />
                    </view>
                </view>
                <view v-if="form.projects && form.projects.length > 0">
                    <u-swipe-action ref="swipeUserList">
                        <u-swipe-action-item v-for="(item, index) in form.projects" :key="index"
                            :name="item.id"
                            :options="getProjectActionOption(item)"
                            @click="projectActionOp">
                            <view class="padding-lr padding-tb-sm">
                                <project-item :detail="item" :colors="colors" @select="select">
                                    <template #op>
                                        <u-button type="primary"
                                            shape="circle"
                                            :color="colors"
                                            text="查看"
                                            :plain="false"
                                            size="small"
                                            @tap="select(item)" />
                                    </template>
                                </project-item>
                            </view>
                        </u-swipe-action-item>
                    </u-swipe-action>
                </view>
                <view v-else class="text-center padding-tb-lg text-gray">
                    <text>暂无服务项目</text>
                </view>
            </view>

            <!-- 服务资料 -->
            <view v-if="datas && datas.length > 0" class="cu-card margin-lr-sm margin-tb-sm radius bg-white padding-sm">
                <scroll-view scroll-y="true" class="scroll-box">
                    <view v-for="(item, index) in datas" :key="index" class="text-content padding-bottom-sm">
                        <view class="text-bold text-lg">{{ item.title }}</view>
                        <view v-for="(detail, dIndex) in item.details" :key="dIndex" class="padding-left-lg">
                            <view class="margin-left-xs">
                                <text>{{ detail.title }}</text>
                                <text v-if="detail.require"
                                    class="text-xs margin-left-xs"
                                    :style="{ color: colors }">(必填)
                                </text>
                                <text v-else class="text-xs margin-left-xs">(选填)</text>
                            </view>
                            <view class="margin-top-sm">
                                <ut-image-upload
                                    ref="upload"
                                    name="file"
                                    v-model="detail.files"
                                    mediaType="image"
                                    :colors="colors"
                                    :max="20"
                                    :headers="headers"
                                    :action="uploadInfo.server + uploadInfo.single || ''"
                                    :preview-image-width="1200"
                                    :width="200"
                                    :height="160"
                                    :border-radius="8"
                                    :disabled="false"
                                    :add="true"
                                    :remove="true"
                                    @uploadSuccess="uploadFaceSuccess($event, detail)">
                                </ut-image-upload>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>

        </view>

        <!-- 底部保存按钮 -->
        <view class="cu-bar bg-white tabbar foot" style="padding: 20rpx;">
            <view class="action">
                <u-button
                    type="primary"
                    :color="colors"
                    :loading="loading"
                    @click="save"
                    text="保存"
                    shape="circle"
                ></u-button>
            </view>
        </view>

        <!-- 项目选择弹窗 -->
        <u-popup :show="showProject"
            mode="bottom"
            round="10"
            :closeable="true"
            :safe-area-inset-bottom="false"
            :mask-close-able="true"
            close-icon-pos="top-left"
            :z-index="998"
            :overlay-style="{zIndex:998}"
            @close="showProject=false">
            <view class="pop-title">项目选择</view>
            <project :project-data="projectData" @selectProject="selectProject" />
        </u-popup>
    </ut-page>
</template>

<script>
let app = getApp()
import { mapState } from 'vuex'
import ProjectItem from '@/pagesA/components/project-item.vue'
import Project from '@/pagesA/care/scheduling/declare/project.vue'

export default {
    components: {
        ProjectItem,
        Project,
    },
    data() {
        return {
            colors: '',
            topWrapHeight: 0,
            workId: '',
            month: '',
            loading: false,
            form: {},
            datas: [],
            showProject: false,
            projectData: [],
        }
    },
    computed: {
        ...mapState({
            community: state => state.init.community,
            uploadInfo: state => state.init.oss,
        }),
        headers() {
            return {
                Token: this.uploadInfo.token,
            }
        },
    },
    onLoad(options) {
        this.workId = options.workId || ''
        this.month = options.month || ''
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
        this.getInfo()
        this.getAllProject()
    },
    methods: {
        getHeight(height, statusHeight) {
            this.topWrapHeight = height
        },
        async getInfo() {
            if (!this.workId) return
            this.loading = true
            const params = {
                communityId: this.community.id,
                workId: this.workId,
            }
            if (this.month) params.month = this.month
            try {
                const { data } = await this.$ut.api('mang/care/proofread/task', params)
                this.form = {
                    ...this.form,
                    ...data,
                    projects: data.projects?.map((item) => ({
                        ...item,
                        id: item.projectId,
                        _id: item.id,
                    })),
                }
                this.datas = data.datas || []
                this.datas.forEach(item => {
                    if (item.details && item.details.length > 0) {
                        item.details.forEach(detail => {
                            if (!detail.files) {
                                this.$set(detail, 'files', [])
                            }
                        })
                    }
                })
            } finally {
                this.loading = false
            }
        },
        async save() {
            this.loading = true
            try {
                await this.$ut.api('mang/care/proofread/errorSave', {
                    communityId: this.community.id,
                    workId: this.workId,
                    projectIds: this.form.projects?.map((item) => item.id || item.projectId),
                    datas: this.datas,
                })
                this.$u.toast('操作成功')
                this.goBack()
            } finally {
                this.loading = false
            }
        },

        goBack() {
            uni.navigateBack()
        },
        async getAllProject() {
            if (!this.workId) return
            const { data } = await this.$ut.api('mang/care/work/project/allRuleListpg', {
                communityId: this.community.id,
                workId: this.form.id,
            })
            this.projectData = data || []
        },
        addProject() {
            this.showProject = true

            if (this.projectData && this.form.projects && this.form.projects.length) {
                this.projectData.forEach(project => {
                    let obj = this.form.projects.find(u => u.id == project.id)
                    if (obj) {
                        this.$set(project, 'checked', true)
                    } else {
                        this.$set(project, 'checked', false)
                    }
                })
            }
        },

        selectProject(projects) {
            this.showProject = false
            if (!this.form.projects) this.$set(this.form, 'projects', [])
            if (!projects || !projects.length) return
            projects.forEach(project => {
                let obj = this.form.projects.find(u => u.id == project.id)
                if (!obj) {
                    this.form.projects.push({
                        id: project.id,
                        projectId: project.id,
                        projectName: project.name,
                        name: project.name,
                        govCode: project.govCode,
                        requireMinDuration: project.minDuration,
                        requireMaxDuration: project.maxDuration,
                    })
                }
            })
        },
        getProjectActionOption(item) {
            const btnDelete = {
                text: '删除项目',
                code: 'delete',
                style: {
                    backgroundColor: '#f56c6c',
                },
            }
            return [btnDelete]
        },
        projectActionOp(data) {
            if (data.code == 'delete') {
                if (this.form.projects && this.form.projects.length) {
                    const index = this.form.projects.findIndex(u => u.id == data.name)
                    if (index >= 0) this.form.projects.splice(index, 1)
                }
            }
            this.$refs['swipeUserList'].closeAll()
        },
        select(item) {
            console.log('选择项目:', item)
        },
        uploadFaceSuccess(res, detail) {
            res.forEach(img => {
                const item = img.data
                detail.files.push(this.uploadInfo.preview + '?file=' + item.name + item.ext)
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.page {
  padding-bottom: 128rpx;
}

.pop-title {
  padding-top: 20rpx;
  text-align: center;
}
</style>
